# src/asr_runner.py
"""
AssemblyAI uploader + transcription helper.

Requires ASSEMBLYAI_API_KEY in environment.

Functions:
- transcribe_file_assemblyai(file_path, options=None) -> dict {'raw_text','segments'}
- transcribe_file(file_path, backend='assemblyai', **kwargs) -> uses assemblyai if chosen
"""

import os, time, requests
from pathlib import Path
from typing import Dict, Any, List

ASSEMBLYAI_ENDPOINT = "https://api.assemblyai.com/v2"

def _get_api_key() -> str:
    key = os.getenv("e73cfb30a1a9486bb8f2488172feaa12")
    if not key:
        raise EnvironmentError("Please set ASSEMBLYAI_API_KEY environment variable.")
    return key

def _upload_file_to_assemblyai(file_path: str) -> str:
    """Upload local file bytes and return an upload_url for the transcript request."""
    api_key = _get_api_key()
    headers = {"authorization": api_key}
    upload_url = f"{ASSEMBLYAI_ENDPOINT}/upload"
    with open(file_path, "rb") as fh:
        # streaming upload
        response = requests.post(upload_url, headers=headers, data=fh)
    response.raise_for_status()
    return response.json()["upload_url"]

def _request_transcript(audio_url: str, options: Dict[str, Any] = None) -> str:
    api_key = _get_api_key()
    headers = {"authorization": api_key, "content-type": "application/json"}
    body = {"audio_url": audio_url}
    if options:
        body.update(options)
    resp = requests.post(f"{ASSEMBLYAI_ENDPOINT}/transcript", headers=headers, json=body)
    resp.raise_for_status()
    return resp.json()["id"]

def _poll_transcript(transcript_id: str, poll_interval: float = 2.0, timeout: float = 600.0) -> Dict:
    api_key = _get_api_key()
    headers = {"authorization": api_key}
    url = f"{ASSEMBLYAI_ENDPOINT}/transcript/{transcript_id}"
    start = time.time()
    backoff = poll_interval
    while True:
        resp = requests.get(url, headers=headers)
        resp.raise_for_status()
        data = resp.json()
        status = data.get("status")
        if status == "completed":
            return data
        if status == "error":
            raise RuntimeError(f"AssemblyAI transcription failed: {data.get('error')}")
        if time.time() - start > timeout:
            raise TimeoutError("Timed out waiting for AssemblyAI transcription.")
        time.sleep(backoff)
        backoff = min(backoff * 1.5, 10.0)

def _build_segments_from_result(result: Dict) -> List[Dict[str, Any]]:
    """Create coarse segments list from AssemblyAI response (utterances or words fallback)."""
    segments = []
    # prefer utterances (speaker-aware chunks)
    if result.get("utterances"):
        for u in result["utterances"]:
            segments.append({
                "start": (u.get("start", 0) or 0) / 1000.0,
                "end": (u.get("end", 0) or 0) / 1000.0,
                "text": u.get("text", ""),
                "confidence": u.get("confidence", 0.0)
            })
    elif result.get("words"):
        # group into coarse segments (~10 words) to create readable chunks
        curr_words = []
        curr_start = None
        curr_end = None
        for w in result["words"]:
            if curr_start is None:
                curr_start = w.get("start")
            curr_end = w.get("end")
            curr_words.append(w.get("text", ""))
            if len(curr_words) >= 10 or w.get("text","").endswith((".", "?", "!")):
                segments.append({
                    "start": (curr_start or 0)/1000.0,
                    "end": (curr_end or 0)/1000.0,
                    "text": " ".join(curr_words),
                    "confidence": w.get("confidence", 0.0)
                })
                curr_words = []
                curr_start = None
                curr_end = None
        if curr_words:
            segments.append({
                "start": (curr_start or 0)/1000.0,
                "end": (curr_end or 0)/1000.0,
                "text": " ".join(curr_words),
                "confidence": 0.0
            })
    else:
        # fallback whole text
        segments = [{"start":0.0, "end":0.0, "text": result.get("text",""), "confidence": 0.0}]
    return segments

def transcribe_file_assemblyai(file_path: str, options: Dict[str, Any] = None, poll_timeout: float = 600.0) -> Dict[str, Any]:
    """Upload file, request transcript, poll, return {'raw_text','segments'}."""
    p = Path(file_path)
    if not p.exists():
        raise FileNotFoundError(file_path)
    upload_url = _upload_file_to_assemblyai(str(p))
    tid = _request_transcript(upload_url, options=options)
    result = _poll_transcript(tid, timeout=poll_timeout)
    segments = _build_segments_from_result(result)
    return {"raw_text": result.get("text", ""), "segments": segments, "raw_result": result}

# Backwards-compatible wrapper
def transcribe_file(file_path: str, backend: str = "assemblyai", **kwargs) -> Dict[str, Any]:
    """
    General wrapper. backend: 'assemblyai' (default) or 'stub'.
    If backend == 'stub', returns a placeholder or sidecar .txt if present.
    """
    if backend == "stub":
        p = Path(file_path)
        txt_alt = p.with_suffix(".txt")
        if txt_alt.exists():
            text = txt_alt.read_text(encoding="utf-8")
            return {"raw_text": text, "segments":[{"start":0.0,"end":0.0,"text":text,"confidence":0.95}]}
        return {"raw_text": f"[TRANSCRIPT_STUB for {p.name}]", "segments":[{"start":0.0,"end":0.0,"text":"", "confidence":0.0}]}
    elif backend == "assemblyai":
        return transcribe_file_assemblyai(file_path, options=kwargs.get("options"), poll_timeout=kwargs.get("poll_timeout",600.0))
    else:
        raise ValueError("Unsupported backend. Use 'assemblyai' or 'stub'.")
