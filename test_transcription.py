#!/usr/bin/env python3
"""
Test script for audio transcription functionality.
"""

import sys
import os
from pathlib import Path

# Add the scripts directory to the path so we can import the module
sys.path.insert(0, str(Path(__file__).parent / "scripts"))

try:
    from run_assemblyai import transcribe_file
    print("✓ Successfully imported transcribe_file")
except ImportError as e:
    print(f"✗ Failed to import transcribe_file: {e}")
    sys.exit(1)

def test_stub_backend():
    """Test the stub backend with available audio files."""
    print("\n=== Testing STUB Backend ===")
    
    # Find the first audio file
    audio_files = list(Path("data").glob("**/*.mp3"))
    if not audio_files:
        print("No audio files found in data directory")
        return False
    
    test_file = audio_files[0]
    print(f"Testing with: {test_file}")
    
    try:
        result = transcribe_file(str(test_file), backend="stub")
        print(f"✓ Stub transcription successful")
        print(f"  Raw text: {result['raw_text']}")
        print(f"  Segments: {len(result['segments'])}")
        return True
    except Exception as e:
        print(f"✗ Stub transcription failed: {e}")
        return False

def test_assemblyai_backend():
    """Test the AssemblyAI backend with available audio files."""
    print("\n=== Testing AssemblyAI Backend ===")
    
    # Find the first audio file
    audio_files = list(Path("data").glob("**/*.mp3"))
    if not audio_files:
        print("No audio files found in data directory")
        return False
    
    test_file = audio_files[0]
    print(f"Testing with: {test_file}")
    
    try:
        print("Attempting to transcribe (this may take a few minutes)...")
        result = transcribe_file(str(test_file), backend="assemblyai")
        print(f"✓ AssemblyAI transcription successful!")
        print(f"  Raw text length: {len(result['raw_text'])} characters")
        print(f"  Number of segments: {len(result['segments'])}")
        print(f"  First 100 characters: {result['raw_text'][:100]}...")
        return True
    except Exception as e:
        print(f"✗ AssemblyAI transcription failed: {e}")
        print("This might be due to:")
        print("  - Network connectivity issues")
        print("  - Invalid API key")
        print("  - Unsupported audio format")
        print("  - File too large")
        return False

def main():
    print("Audio Transcription Test Suite")
    print("=" * 40)
    
    # Test stub backend first (should always work)
    stub_success = test_stub_backend()
    
    # Test AssemblyAI backend
    assemblyai_success = test_assemblyai_backend()
    
    print("\n" + "=" * 40)
    print("TEST SUMMARY:")
    print(f"  Stub backend: {'✓ PASS' if stub_success else '✗ FAIL'}")
    print(f"  AssemblyAI backend: {'✓ PASS' if assemblyai_success else '✗ FAIL'}")
    
    if assemblyai_success:
        print("\n🎉 All tests passed! Your transcription is working correctly.")
    elif stub_success:
        print("\n⚠️  Stub backend works, but AssemblyAI has issues. Check your API key and network.")
    else:
        print("\n❌ Both backends failed. Check your code and file paths.")

if __name__ == "__main__":
    main()
