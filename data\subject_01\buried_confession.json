{"raw_text": "They applauded. The demo felt good. I usually say I drove the architecture because it's what people expect, but behind the applause, I mostly copied patterns, reused scripts, and assembled other people's modules. Once in a while, I told myself that stitching counts as designing. It's not a lie so much as a convenience.", "segments": [{"start": 1.52, "end": 2.28, "text": "They applauded.", "confidence": 0.9963867}, {"start": 2.28, "end": 4.16, "text": "The demo felt good.", "confidence": 0.9995117}, {"start": 5.36, "end": 8.96, "text": "I usually say I drove the architecture because it's what", "confidence": 1.0}, {"start": 8.96, "end": 15.6, "text": "people expect, but behind the applause, I mostly copied patterns,", "confidence": 0.9995117}, {"start": 15.84, "end": 22.16, "text": "reused scripts, and assembled other people's modules.", "confidence": 0.8486328}, {"start": 24.0, "end": 27.28, "text": "Once in a while, I told myself that stitching counts", "confidence": 0.9992676}, {"start": 27.28, "end": 28.78, "text": "as designing.", "confidence": 0.99975586}, {"start": 29.18, "end": 32.14, "text": "It's not a lie so much as a convenience.", "confidence": 0.9998372}], "raw_result": {"id": "9b6049b4-1527-40b4-9a0e-8743a94167bf", "language_model": "assemblyai_default", "acoustic_model": "assemblyai_default", "language_code": "en_us", "status": "completed", "audio_url": "https://cdn.assemblyai.com/upload/66d722df-4187-4433-860a-d583793dbd6e", "text": "They applauded. The demo felt good. I usually say I drove the architecture because it's what people expect, but behind the applause, I mostly copied patterns, reused scripts, and assembled other people's modules. Once in a while, I told myself that stitching counts as designing. It's not a lie so much as a convenience.", "words": [{"text": "They", "start": 1520, "end": 1680, "confidence": 0.9946289, "speaker": null}, {"text": "applauded.", "start": 1680, "end": 2280, "confidence": 0.9963867, "speaker": null}, {"text": "The", "start": 2280, "end": 2480, "confidence": 0.9995117, "speaker": null}, {"text": "demo", "start": 2480, "end": 3040, "confidence": 0.99902344, "speaker": null}, {"text": "felt", "start": 3200, "end": 3760, "confidence": 0.99609375, "speaker": null}, {"text": "good.", "start": 3760, "end": 4160, "confidence": 0.9995117, "speaker": null}, {"text": "I", "start": 5360, "end": 5680, "confidence": 0.98876953, "speaker": null}, {"text": "usually", "start": 5680, "end": 6240, "confidence": 0.99975586, "speaker": null}, {"text": "say", "start": 6240, "end": 6560, "confidence": 0.99902344, "speaker": null}, {"text": "I", "start": 6560, "end": 6800, "confidence": 0.99902344, "speaker": null}, {"text": "drove", "start": 6800, "end": 7160, "confidence": 0.96191406, "speaker": null}, {"text": "the", "start": 7160, "end": 7280, "confidence": 0.99853516, "speaker": null}, {"text": "architecture", "start": 7280, "end": 8000, "confidence": 0.99990237, "speaker": null}, {"text": "because", "start": 8080, "end": 8400, "confidence": 0.9995117, "speaker": null}, {"text": "it's", "start": 8400, "end": 8760, "confidence": 0.99869794, "speaker": null}, {"text": "what", "start": 8760, "end": 8960, "confidence": 1.0, "speaker": null}, {"text": "people", "start": 8960, "end": 9280, "confidence": 1.0, "speaker": null}, {"text": "expect,", "start": 9760, "end": 10160, "confidence": 0.99902344, "speaker": null}, {"text": "but", "start": 10720, "end": 11080, "confidence": 0.99560547, "speaker": null}, {"text": "behind", "start": 11080, "end": 11440, "confidence": 0.9995117, "speaker": null}, {"text": "the", "start": 11440, "end": 11760, "confidence": 0.99902344, "speaker": null}, {"text": "applause,", "start": 11760, "end": 12320, "confidence": 0.9012044, "speaker": null}, {"text": "I", "start": 12640, "end": 13040, "confidence": 0.9995117, "speaker": null}, {"text": "mostly", "start": 13040, "end": 13680, "confidence": 0.99975586, "speaker": null}, {"text": "copied", "start": 13840, "end": 14480, "confidence": 0.92626953, "speaker": null}, {"text": "patterns,", "start": 14800, "end": 15600, "confidence": 0.9995117, "speaker": null}, {"text": "reused", "start": 15840, "end": 16480, "confidence": 0.998291, "speaker": null}, {"text": "scripts,", "start": 16720, "end": 17600, "confidence": 0.9124756, "speaker": null}, {"text": "and", "start": 17920, "end": 18280, "confidence": 0.99121094, "speaker": null}, {"text": "assembled", "start": 18280, "end": 19240, "confidence": 0.9984131, "speaker": null}, {"text": "other", "start": 19240, "end": 19600, "confidence": 0.9995117, "speaker": null}, {"text": "people's", "start": 19680, "end": 20480, "confidence": 0.99609375, "speaker": null}, {"text": "modules.", "start": 20960, "end": 22160, "confidence": 0.8486328, "speaker": null}, {"text": "Once", "start": 24000, "end": 24320, "confidence": 0.9995117, "speaker": null}, {"text": "in", "start": 24320, "end": 24520, "confidence": 0.9980469, "speaker": null}, {"text": "a", "start": 24520, "end": 24680, "confidence": 0.97802734, "speaker": null}, {"text": "while,", "start": 24680, "end": 24960, "confidence": 1.0, "speaker": null}, {"text": "I", "start": 25040, "end": 25400, "confidence": 0.9995117, "speaker": null}, {"text": "told", "start": 25400, "end": 25680, "confidence": 1.0, "speaker": null}, {"text": "myself", "start": 25680, "end": 26120, "confidence": 0.765625, "speaker": null}, {"text": "that", "start": 26120, "end": 26400, "confidence": 0.9995117, "speaker": null}, {"text": "stitching", "start": 26400, "end": 26880, "confidence": 0.9996745, "speaker": null}, {"text": "counts", "start": 26880, "end": 27280, "confidence": 0.9992676, "speaker": null}, {"text": "as", "start": 27280, "end": 27600, "confidence": 0.99658203, "speaker": null}, {"text": "designing.", "start": 28140, "end": 28780, "confidence": 0.99975586, "speaker": null}, {"text": "It's", "start": 29180, "end": 29540, "confidence": 0.9773763, "speaker": null}, {"text": "not", "start": 29540, "end": 29700, "confidence": 1.0, "speaker": null}, {"text": "a", "start": 29700, "end": 29860, "confidence": 1.0, "speaker": null}, {"text": "lie", "start": 29860, "end": 30220, "confidence": 0.9970703, "speaker": null}, {"text": "so", "start": 30220, "end": 30500, "confidence": 1.0, "speaker": null}, {"text": "much", "start": 30500, "end": 30660, "confidence": 1.0, "speaker": null}, {"text": "as", "start": 30660, "end": 30860, "confidence": 1.0, "speaker": null}, {"text": "a", "start": 30860, "end": 31180, "confidence": 1.0, "speaker": null}, {"text": "convenience.", "start": 31340, "end": 32140, "confidence": 0.9998372, "speaker": null}], "utterances": null, "confidence": 0.98526174, "audio_duration": 35, "punctuate": true, "format_text": true, "dual_channel": null, "webhook_url": null, "webhook_status_code": null, "webhook_auth": false, "webhook_auth_header_name": null, "speed_boost": false, "auto_highlights_result": null, "auto_highlights": false, "audio_start_from": null, "audio_end_at": null, "word_boost": [], "boost_param": null, "prompt": null, "keyterms_prompt": [], "filter_profanity": false, "redact_pii": false, "redact_pii_audio": false, "redact_pii_audio_quality": null, "redact_pii_audio_options": null, "redact_pii_policies": null, "redact_pii_sub": null, "speaker_labels": false, "speaker_options": null, "content_safety": false, "iab_categories": false, "content_safety_labels": {"status": "unavailable", "results": [], "summary": {}}, "iab_categories_result": {"status": "unavailable", "results": [], "summary": {}}, "language_detection": false, "language_detection_options": null, "language_confidence_threshold": null, "language_confidence": null, "custom_spelling": null, "throttled": false, "auto_chapters": false, "summarization": false, "summary_type": null, "summary_model": null, "custom_topics": false, "topics": [], "speech_threshold": null, "speech_model": null, "chapters": null, "disfluencies": false, "entity_detection": false, "sentiment_analysis": false, "sentiment_analysis_results": null, "entities": null, "speakers_expected": null, "summary": null, "custom_topics_results": null, "is_deleted": null, "multichannel": null, "project_id": 533425, "token_id": 534446}}