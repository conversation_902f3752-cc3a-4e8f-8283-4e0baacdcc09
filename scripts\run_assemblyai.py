# src/asr_runner.py
"""
AssemblyAI + stub ASR runner.

Provides:
- transcribe_file_assemblyai(file_path, options=None, poll_timeout=600.0)
- transcribe_file(file_path, backend='assemblyai'|'stub', **kwargs)   <-- the wrapper callers expect
"""

import os
import time
import requests
from pathlib import Path
from typing import Dict, Any, List

ASSEMBLYAI_ENDPOINT = "https://api.assemblyai.com/v2"

def _get_api_key() -> str:
    key = os.getenv("e73cfb30a1a9486bb8f2488172feaa12")
    if not key:
        raise EnvironmentError("Please set ASSEMBLYAI_API_KEY environment variable.")
    return key

def _upload_file_to_assemblyai(file_path: str) -> str:
    api_key = _get_api_key()
    headers = {"authorization": api_key}
    upload_url = f"{ASSEMBLYAI_ENDPOINT}/upload"
    with open(file_path, "rb") as fh:
        # streaming upload of bytes
        resp = requests.post(upload_url, headers=headers, data=fh)
    resp.raise_for_status()
    return resp.json()["upload_url"]

def _request_transcript(audio_url: str, options: Dict[str, Any] = None) -> str:
    api_key = _get_api_key()
    headers = {"authorization": api_key, "content-type": "application/json"}
    body = {"audio_url": audio_url}
    if options:
        body.update(options)
    resp = requests.post(f"{ASSEMBLYAI_ENDPOINT}/transcript", headers=headers, json=body)
    resp.raise_for_status()
    return resp.json()["id"]

def _poll_transcript(transcript_id: str, poll_interval: float = 2.0, timeout: float = 600.0) -> Dict[str, Any]:
    api_key = _get_api_key()
    headers = {"authorization": api_key}
    url = f"{ASSEMBLYAI_ENDPOINT}/transcript/{transcript_id}"
    start = time.time()
    backoff = poll_interval
    while True:
        resp = requests.get(url, headers=headers)
        resp.raise_for_status()
        data = resp.json()
        status = data.get("status")
        if status == "completed":
            return data
        if status == "error":
            raise RuntimeError(f"AssemblyAI transcription failed: {data.get('error')}")
        if time.time() - start > timeout:
            raise TimeoutError("Timed out waiting for AssemblyAI transcription.")
        time.sleep(backoff)
        backoff = min(backoff * 1.5, 10.0)

def _build_segments_from_result(result: Dict[str, Any]) -> List[Dict[str, Any]]:
    segments: List[Dict[str, Any]] = []
    if result.get("utterances"):
        for u in result["utterances"]:
            segments.append({
                "start": (u.get("start", 0) or 0) / 1000.0,
                "end": (u.get("end", 0) or 0) / 1000.0,
                "text": u.get("text", ""),
                "confidence": u.get("confidence", 0.0)
            })
    elif result.get("words"):
        curr_words = []
        curr_start = None
        curr_end = None
        for w in result["words"]:
            if curr_start is None:
                curr_start = w.get("start")
            curr_end = w.get("end")
            curr_words.append(w.get("text", ""))
            if len(curr_words) >= 10 or w.get("text","").endswith((".", "?", "!")):
                segments.append({
                    "start": (curr_start or 0)/1000.0,
                    "end": (curr_end or 0)/1000.0,
                    "text": " ".join(curr_words),
                    "confidence": w.get("confidence", 0.0)
                })
                curr_words = []
                curr_start = None
                curr_end = None
        if curr_words:
            segments.append({
                "start": (curr_start or 0)/1000.0,
                "end": (curr_end or 0)/1000.0,
                "text": " ".join(curr_words),
                "confidence": 0.0
            })
    else:
        segments = [{"start": 0.0, "end": 0.0, "text": result.get("text", ""), "confidence": 0.0}]
    return segments

def transcribe_file_assemblyai(file_path: str, options: Dict[str, Any] = None, poll_timeout: float = 600.0) -> Dict[str, Any]:
    p = Path(file_path)
    if not p.exists():
        raise FileNotFoundError(file_path)
    upload_url = _upload_file_to_assemblyai(str(p))
    tid = _request_transcript(upload_url, options=options)
    result = _poll_transcript(tid, timeout=poll_timeout)
    segments = _build_segments_from_result(result)
    return {"raw_text": result.get("text", ""), "segments": segments, "raw_result": result}

# simple stub backend for offline testing
def transcribe_file_stub(file_path: str) -> Dict[str, Any]:
    p = Path(file_path)
    txt_sidecar = p.with_suffix(".txt")
    if txt_sidecar.exists():
        t = txt_sidecar.read_text(encoding="utf-8")
        return {"raw_text": t, "segments": [{"start":0.0, "end": 0.0, "text": t, "confidence": 0.95}]}
    return {"raw_text": f"[TRANSCRIPT_STUB for {p.name}]", "segments": [{"start":0.0, "end":0.0, "text":"", "confidence":0.0}]}

def transcribe_file(file_path: str, backend: str = "assemblyai", **kwargs) -> Dict[str, Any]:
    """
    Generic wrapper used by the orchestrator.
    - backend: 'assemblyai' or 'stub'
    - kwargs: passed to the underlying function (e.g., options for assemblyai)
    """
    if backend == "stub":
        return transcribe_file_stub(file_path)
    elif backend == "assemblyai":
        return transcribe_file_assemblyai(file_path, options=kwargs.get("options"), poll_timeout=kwargs.get("poll_timeout", 600.0))
    else:
        raise ValueError("Unsupported backend: choose 'assemblyai' or 'stub'.")
